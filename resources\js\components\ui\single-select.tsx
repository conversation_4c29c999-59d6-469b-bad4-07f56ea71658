import * as React from 'react'
import { Check, ChevronsUpDown, Loader2, Plus } from 'lucide-react'
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/command'

import { cn } from '@/lib/utils'

export interface Option {
  label: string
  value: string | number
  icon?: React.ReactNode
}

interface SingleSelectProps {
  options: Option[]
  value: string | number | null
  onChange: (value: string | number | null) => void
  placeholder?: string
  label?: string
  icon?: React.ReactNode
  disabled?: boolean
  className?: string
  onCreate?: (label: string) => Promise<Option>
  allowCustomValue?: boolean
  createPrefix?: string
  tabIndex?: number
}

export function SingleSelect({
  options,
  value,
  onChange,
  placeholder = 'Válassz...',
  label,
  icon,
  disabled = false,
  className,
  onCreate,
  allowCustomValue = false,
  createPrefix = 'Új hozzáadása: ',
  tabIndex,
}: SingleSelectProps) {
  const [open, setOpen] = React.useState(false)
  const [searchTerm, setSearchTerm] = React.useState('')
  const [creating, setCreating] = React.useState(false)
  const containerRef = React.useRef<HTMLDivElement>(null)

  // Click outside handler
  React.useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setOpen(false)
        setSearchTerm('')
      }
    }

    if (open) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [open])

  const trimmedSearch = searchTerm.trim()
  const selectedOption = options.find((option) => option.value === value)

  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(trimmedSearch.toLowerCase())
  )

  const exists = options.some(
    (option) => option.label.toLowerCase() === trimmedSearch.toLowerCase()
  )

  const canCreateAPI = !!onCreate && trimmedSearch.length > 1 && !exists
  const canCreateLocal = allowCustomValue && trimmedSearch.length > 1 && !exists

  const handleCreate = async () => {
    if (creating) return

    if (canCreateAPI && onCreate) {
      setCreating(true)
      try {
        const newOption = await onCreate(trimmedSearch)
        onChange(newOption.value)
        setSearchTerm('')
        setOpen(false)
      } catch (e) {
        console.error('Hiba új elem létrehozásakor:', e)
      } finally {
        setCreating(false)
      }
    } else if (canCreateLocal) {
      onChange(trimmedSearch)
      setSearchTerm('')
      setOpen(false)
    }
  }

  return (
    <div className={cn('w-full', className)} ref={containerRef}>
      {label && <label className="mb-2 block text-sm font-medium text-foreground">{label}</label>}

      <div className="relative">
        <div
          onClick={() => !disabled && setOpen(!open)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' || e.key === ' ') {
              e.preventDefault()
              !disabled && setOpen(!open)
            }
          }}
          tabIndex={disabled ? -1 : (tabIndex || 0)}
          className={cn(
            'relative flex min-h-10 w-full items-center justify-between rounded-md border border-input bg-background text-sm shadow-xs transition',
            'hover:border-accent-foreground/25',
            'focus:border-green-500 focus:ring-green-500/20 focus:ring-[3px] focus:outline-none',
            icon ? 'pl-10 pr-3' : 'px-3',
            'py-2',
            disabled && 'cursor-not-allowed opacity-50',
            !disabled && 'cursor-pointer'
          )}
        >
          {icon && (
            <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
              {icon}
            </div>
          )}
          <span className={cn('truncate', !selectedOption && 'text-muted-foreground')}>
            {selectedOption?.label || placeholder}
          </span>
          <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
        </div>

        {open && (
          <div className="absolute top-full left-0 right-0 z-[9999] mt-1 bg-background border border-input rounded-md shadow-lg p-0">
          <Command shouldFilter={false}>
            <CommandInput
              placeholder="Keresés..."
              value={searchTerm}
              onValueChange={setSearchTerm}
              autoFocus={true}
              className="focus:ring-0 focus:ring-offset-0"
            />
            {filteredOptions.length === 0 && !canCreateAPI && !canCreateLocal && (
              <CommandEmpty>Nincs találat</CommandEmpty>
            )}
            <CommandGroup className="max-h-64 overflow-y-auto">
              {filteredOptions.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.label}
                  onSelect={() => {
                    onChange(option.value)
                    setSearchTerm('')
                    setOpen(false)
                  }}
                  className={cn(
                    'cursor-pointer',
                    value === option.value && 'bg-muted'
                  )}
                >
                  <div className="mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary">
                    {value === option.value && <Check className="h-3 w-3 text-primary" />}
                  </div>
                  {option.icon && <span className="mr-2 h-4 w-4 text-muted-foreground">{option.icon}</span>}
                  <span className="truncate">{option.label}</span>
                </CommandItem>
              ))}

              {(canCreateAPI || canCreateLocal) && (
                <CommandItem
                  onSelect={handleCreate}
                  disabled={creating}
                  className="cursor-pointer border-t border-border text-primary"
                >
                  <div className="mr-2 flex h-4 w-4 items-center justify-center">
                    {creating ? <Loader2 className="h-3 w-3 animate-spin" /> : <Plus className="h-3 w-3" />}
                  </div>
                  <span className="truncate">
                    {createPrefix}
                    <strong>{trimmedSearch}</strong>
                  </span>
                </CommandItem>
              )}
            </CommandGroup>
          </Command>
          </div>
        )}
      </div>
    </div>
  )
}
import PostalVehicleForm from '@/components/admin/PostalVehicleForm';
import { ImportDropzone } from '@/components/shared/ImportDropzone';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { PageProps } from '@inertiajs/core';
import { Head, router, usePage } from '@inertiajs/react';
import { ColumnDef } from '@tanstack/react-table';
import { AlertCircle, AlertTriangle, Car, Download, Plus, Search, Upload } from 'lucide-react';
import { useEffect, useState } from 'react';

interface PostalVehicle {
    id: number;
    license_plate: string;
    type: string;
    chassis_number: string;
    category: 'N1' | 'N2' | 'N3';
    contract_classification: string;
    manufacturing_year: number;
    fuel_type: string;
    authority_inspection_valid_until: string;
    garage_id: number;
    garage_central_email: string;
    service_provider_id: number;
    technical_staff_name: string;
    technical_staff_email: string;
    technical_manager_name: string;
    technical_manager_email: string;
    garage?: {
        id: number;
        name: string;
    };
    service_provider?: {
        id: number;
        name: string;
    };
}

interface Garage {
    id: number;
    name: string;
    postal_code?: string;
    city?: string;
    address?: string;
}

interface ServiceProvider {
    id: number;
    name: string;
    city: string;
}

interface Props extends PageProps {
    vehicles: PostalVehicle[];
    garages: Garage[];
    serviceProviders: ServiceProvider[];
    auth: {
        user: {
            permissions: string[];
        };
    };
    flash: {
        import_errors?: string[];
        success?: string;
        error?: string;
    };
}

const breadcrumbs: BreadcrumbItem[] = [{ title: 'Postai járművek', href: '/admin/postal-vehicles' }];

export default function PostalVehicleIndex({ vehicles, garages, serviceProviders, auth }: Props) {
    const [open, setOpen] = useState(false);
    const [editing, setEditing] = useState<PostalVehicle | null>(null);
    const [globalFilter, setGlobalFilter] = useState('');
    const [deleteTarget, setDeleteTarget] = useState<PostalVehicle | null>(null);
    const [isImportModalOpen, setIsImportModalOpen] = useState(false);
    const { flash = {} } = usePage<Props>().props;
    const importErrors = flash.import_errors || [];

    const canImport = auth.user.permissions.includes('import_postal_vehicles');

    useEffect(() => {
        if (importErrors.length > 0) {
            setIsImportModalOpen(true);
        }
    }, [importErrors]);

    const handleAdd = () => {
        setEditing(null);
        setOpen(true);
    };

    const handleEdit = (vehicle: PostalVehicle) => {
        setEditing(vehicle);
        setOpen(true);
    };

    const confirmDelete = (vehicle: PostalVehicle) => {
        setDeleteTarget(vehicle);
    };

    const handleDelete = () => {
        if (deleteTarget) {
            router.delete(route('admin.postal-vehicles.destroy', deleteTarget.id));
            setDeleteTarget(null);
        }
    };

    const handleExport = () => {
        window.location.href = route('admin.postal-vehicles.export');
    };

    const handleDownloadSample = () => {
        window.location.href = route('admin.postal-vehicles.download-sample');
    };

    const getCategoryBadge = (category: string) => {
        const variants = {
            N1: 'bg-blue-100 text-blue-800',
            N2: 'bg-green-100 text-green-800',
            N3: 'bg-purple-100 text-purple-800',
        };
        return <Badge className={variants[category as keyof typeof variants] || 'bg-gray-100 text-gray-800'}>{category}</Badge>;
    };

    const getInspectionStatus = (date: string) => {
        const inspectionDate = new Date(date);
        const now = new Date();
        const diffTime = inspectionDate.getTime() - now.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays < 0) {
            return <Badge variant="destructive">Lejárt</Badge>;
        } else if (diffDays <= 30) {
            return <Badge className="bg-yellow-100 text-yellow-800">Hamarosan lejár</Badge>;
        } else {
            return (
                <Badge variant="default" className="bg-green-100 text-green-800">
                    Érvényes
                </Badge>
            );
        }
    };

    const columns: ColumnDef<PostalVehicle>[] = [
        {
            accessorKey: 'license_plate',
            header: 'Rendszám',
            cell: ({ row }) => <div className="font-mono font-medium">{row.original.license_plate}</div>,
        },
        {
            accessorKey: 'type',
            header: 'Típus',
        },
        {
            accessorKey: 'category',
            header: 'Kategória',
            cell: ({ row }) => getCategoryBadge(row.original.category),
        },
        {
            accessorKey: 'manufacturing_year',
            header: 'Gyártási év',
        },
        {
            accessorKey: 'garage.name',
            header: 'Garázs',
            cell: ({ row }) => row.original.garage?.name || '-',
        },
        {
            accessorKey: 'service_provider.name',
            header: 'Szerviz',
            cell: ({ row }) => row.original.service_provider?.name || '-',
        },
        {
            accessorKey: 'authority_inspection_valid_until',
            header: 'Hatósági vizsga',
            cell: ({ row }) => (
                <div className="space-y-1">
                    <div className="text-sm">{new Date(row.original.authority_inspection_valid_until).toLocaleDateString('hu-HU')}</div>
                    {getInspectionStatus(row.original.authority_inspection_valid_until)}
                </div>
            ),
        },
        {
            accessorKey: 'technical_staff_name',
            header: 'Műszaki munkatárs',
        },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Postai járművek" />

            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                        <div className="flex items-center gap-2">
                            <Car className="h-6 w-6 text-primary" />
                            <h1 className="text-2xl font-bold text-gray-900">Postai járművek</h1>
                        </div>
                        <Badge variant="secondary" className="text-sm">
                            {vehicles.length} jármű
                        </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                        <div className="relative">
                            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                            <Input
                                placeholder="Keresés..."
                                value={globalFilter}
                                onChange={(e) => setGlobalFilter(e.target.value)}
                                className="w-64 pl-10"
                            />
                        </div>
                        <Button variant="outline" onClick={handleExport}>
                            <Download className="mr-2 h-4 w-4" />
                            Export
                        </Button>
                        {canImport && (
                            <Dialog open={isImportModalOpen} onOpenChange={setIsImportModalOpen}>
                                <DialogTrigger asChild>
                                    <Button variant="outline">
                                        <Upload className="mr-2 h-4 w-4" />
                                        Import
                                    </Button>
                                </DialogTrigger>
                                <DialogContent className="max-w-2xl">
                                    <DialogHeader>
                                        <DialogTitle>Postai járművek importálása</DialogTitle>
                                        <DialogDescription>Töltsön fel egy CSV vagy Excel fájlt a járművek tömeges hozzáadásához.</DialogDescription>
                                    </DialogHeader>
                                    <div className="space-y-4 py-4">
                                        {importErrors.length > 0 && (
                                            <Alert variant="destructive">
                                                <AlertCircle className="h-4 w-4" />
                                                <AlertTitle>Importálási hibák</AlertTitle>
                                                <AlertDescription>
                                                    <ul className="mt-2 list-disc space-y-1 pl-5">
                                                        {importErrors.map((error, index) => (
                                                            <li key={index}>{error}</li>
                                                        ))}
                                                    </ul>
                                                </AlertDescription>
                                            </Alert>
                                        )}
                                        <ImportDropzone
                                            uploadRouteName="admin.postal-vehicles.import"
                                            onDownloadSample={handleDownloadSample}
                                            onUploadSuccess={() => setIsImportModalOpen(false)}
                                        />
                                    </div>
                                </DialogContent>
                            </Dialog>
                        )}
                        <Dialog open={open} onOpenChange={setOpen}>
                            <DialogTrigger asChild>
                                <Button onClick={handleAdd} className="bg-primary text-white shadow-sm hover:bg-green-600">
                                    <Plus className="mr-2 h-4 w-4" />
                                    Új jármű
                                </Button>
                            </DialogTrigger>
                            <DialogContent
                                className="flex max-h-[90vh] max-w-5xl flex-col p-0 md:max-w-6xl"
                                onInteractOutside={(e) => {
                                    e.preventDefault();
                                }}
                            >
                                <DialogHeader className="p-6 pb-0">
                                    <DialogTitle>{editing ? 'Jármű szerkesztése' : 'Új jármű létrehozása'}</DialogTitle>
                                    <DialogDescription>Töltsd ki az alábbi űrlapot a jármű adatainak mentéséhez.</DialogDescription>
                                </DialogHeader>

                                <PostalVehicleForm
                                    vehicle={editing}
                                    garages={garages}
                                    serviceProviders={serviceProviders}
                                    onCancel={() => setOpen(false)}
                                />
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>

                <DataTable
                    columns={columns}
                    data={vehicles}
                    onEdit={handleEdit}
                    onDelete={confirmDelete}
                    globalFilter={globalFilter}
                    exportFileName="postai_jarmuvek"
                    pageSize={10}
                    showExportButton={false}
                />
            </div>

            {/* Delete confirmation dialog */}
            <AlertDialog open={!!deleteTarget} onOpenChange={() => setDeleteTarget(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle className="flex items-center gap-2">
                            <AlertTriangle className="h-5 w-5 text-red-500" />
                            Jármű törlése
                        </AlertDialogTitle>
                    </AlertDialogHeader>
                    <p>
                        Biztosan törölni szeretné a <strong>{deleteTarget?.license_plate}</strong> rendszámú járművet? Ez a művelet nem vonható
                        vissza.
                    </p>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Mégse</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDelete} className="bg-red-600 hover:bg-red-700">
                            Törlés
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}

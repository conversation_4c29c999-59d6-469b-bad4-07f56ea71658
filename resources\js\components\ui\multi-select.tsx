import * as React from 'react'
import { Check, ChevronsUpDown, X, User, Mail, Phone, MapPin } from 'lucide-react'
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem } from '@/components/ui/command'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'
import { cn } from '@/lib/utils'


// Enhanced MultiSelect component
interface Option {
  label: string
  value: string | number
  icon?: React.ReactNode
}

interface MultiSelectProps {
  options: Option[]
  selected: (string | number)[]
  onChange: (selected: (string | number)[]) => void
  placeholder?: string
  label?: string
  icon?: React.ReactNode
  disabled?: boolean
  maxDisplay?: number
  className?: string
  modal?: boolean
}

export function MultiSelect({
  options,
  selected,
  onChange,
  placeholder = 'Válassz...',
  label,
  icon,
  disabled = false,
  maxDisplay = 3,
  className,
  modal = false,
}: MultiSelectProps) {
  const [open, setOpen] = React.useState(false)
  const [searchTerm, setSearchTerm] = React.useState('')
  const dropdownRef = React.useRef<HTMLDivElement>(null)

  const toggleOption = (value: string | number) => {
    if (disabled) return
    
    if (selected.includes(value)) {
      onChange(selected.filter((v) => v !== value))
    } else {
      onChange([...selected, value])
    }
  }

  const removeOption = (value: string | number, e: React.MouseEvent) => {
    e.stopPropagation()
    onChange(selected.filter((v) => v !== value))
  }

  const clearAll = (e: React.MouseEvent) => {
    e.stopPropagation()
    onChange([])
  }

  const filteredOptions = options.filter((option) =>
    typeof option.label === 'string' && option.label.toLowerCase().includes(searchTerm.toLowerCase())

  )

  const selectedOptions = options.filter((option) => selected.includes(option.value))
  const displayedOptions = selectedOptions.slice(0, maxDisplay)
  const remainingCount = selectedOptions.length - maxDisplay

  // Click outside kezelő modalban
  React.useEffect(() => {
    if (!modal || !open) return

    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setOpen(false)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [modal, open])

  React.useEffect(() => {
    return () => {
      setOpen(false);
    };
  }, []);

  // Modal esetén egyedi dropdown, egyébként Popover
  if (modal) {
    return (
      <div className={cn("w-full relative", className)}>
        {label && (
          <label className="mb-2 block text-sm font-medium text-foreground">
            {label}
          </label>
        )}

        <div className="relative" ref={dropdownRef}>
          <div
            onClick={() => !disabled && setOpen(!open)}
            className={cn(
              'relative flex min-h-10 w-full items-center justify-between rounded-md border border-input bg-background text-sm shadow-xs transition-[color,box-shadow] outline-none',
              'hover:border-accent-foreground/25',
              // Focus stílus ugyanaz, mint az input mezőknél
              open && 'border-green-500 ring-green-500/20 ring-[3px]',
              disabled && 'cursor-not-allowed opacity-50',
              !disabled && 'cursor-pointer',
              icon && 'pl-10',
              !icon && 'px-3',
              'py-2'
            )}
          >
            {icon && (
              <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                {icon}
              </div>
            )}

            <div className="flex flex-1 flex-wrap gap-1">
              {selected.length === 0 ? (
                <span className="text-muted-foreground">{placeholder}</span>
              ) : (
                <>
                  {displayedOptions.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-center gap-1 rounded-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 text-xs"
                    >
                      {option.icon && <span className="h-3 w-3">{option.icon}</span>}
                      <span className="truncate">{option.label}</span>
                      {!disabled && (
                        <button
                          type="button"
                          onClick={(e) => removeOption(option.value, e)}
                          className="ml-1 rounded-full p-0.5 hover:bg-secondary-foreground/20"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                    </div>
                  ))}
                  {remainingCount > 0 && (
                    <div className="flex items-center rounded-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 text-xs text-muted-foreground">
                      +{remainingCount} további
                    </div>
                  )}
                </>
              )}
            </div>

            <div className="flex items-center gap-2">
              {selected.length > 0 && !disabled && (
                <button
                  type="button"
                  onClick={clearAll}
                  className="rounded-full p-1 hover:bg-secondary"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
              <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
            </div>
          </div>

          {open && (
            <div className="absolute top-full left-0 right-0 z-[9999] mt-1 bg-background border border-input rounded-md shadow-lg">
              <Command shouldFilter={false}>
                <CommandInput
                  placeholder="Keresés..."
                  value={searchTerm}
                  onValueChange={setSearchTerm}
                  autoFocus={true}
                  className="focus:ring-0 focus:ring-offset-0"
                />

                {filteredOptions.length === 0 ? (
                  <CommandEmpty>Nincs találat</CommandEmpty>
                ) : (
                  <CommandGroup className="max-h-64 overflow-y-auto">
                    {filteredOptions.map((option) => (
                      <CommandItem
                        key={option.value}
                        value={option.label}
                        onSelect={() => toggleOption(option.value)}
                        className={cn(
                          "cursor-pointer",
                          selected.includes(option.value) && "bg-gray-100 dark:bg-gray-800"
                        )}
                      >
                        <div className="mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary">
                          {selected.includes(option.value) && (
                            <Check className="h-3 w-3 text-primary" />
                          )}
                        </div>
                        {option.icon && (
                          <span className="mr-2 h-4 w-4 text-muted-foreground">
                            {option.icon}
                          </span>
                        )}
                        <span className="truncate">{option.label}</span>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                )}
              </Command>
            </div>
          )}
        </div>
      </div>
    )
  }

  return (
    <div className={cn("w-full", className)}>
      {label && (
        <label className="mb-2 block text-sm font-medium text-foreground">
          {label}
        </label>
      )}

      <Popover open={open} onOpenChange={setOpen}>
        <PopoverTrigger asChild>
          <div
            className={cn(
              'relative flex min-h-10 w-full items-center justify-between rounded-md border border-input bg-background text-sm shadow-xs transition-[color,box-shadow] outline-none',
              'hover:border-accent-foreground/25',
              // Focus stílus ugyanaz, mint az input mezőknél
              open && 'border-green-500 ring-green-500/20 ring-[3px]',
              disabled && 'cursor-not-allowed opacity-50',
              !disabled && 'cursor-pointer',
              icon && 'pl-10',
              !icon && 'px-3',
              'py-2'
            )}
          >
            {icon && (
              <div className="absolute left-3 top-1/2 -translate-y-1/2 text-muted-foreground">
                {icon}
              </div>
            )}
            
            <div className="flex flex-1 flex-wrap gap-1">
              {selected.length === 0 ? (
                <span className="text-muted-foreground">{placeholder}</span>
              ) : (
                <>
                  {displayedOptions.map((option) => (
                    <div
                      key={option.value}
                      className="flex items-center gap-1 rounded-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 text-xs"
                    >
                      {option.icon && <span className="h-3 w-3">{option.icon}</span>}
                      <span className="truncate">{option.label}</span>
                      {!disabled && (
                        <button
                          type="button"
                          onClick={(e) => removeOption(option.value, e)}
                          className="ml-1 rounded-full p-0.5 hover:bg-secondary-foreground/20"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                    </div>
                  ))}
                  {remainingCount > 0 && (
                    <div className="flex items-center rounded-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 text-xs text-muted-foreground">
                      +{remainingCount} további
                    </div>
                  )}
                </>
              )}
            </div>
            
            <div className="flex items-center gap-2">
              {selected.length > 0 && !disabled && (
                <button
                  type="button"
                  onClick={clearAll}
                  className="rounded-full p-1 hover:bg-secondary"
                >
                  <X className="h-3 w-3" />
                </button>
              )}
              <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
            </div>
          </div>
        </PopoverTrigger>
        
        <PopoverContent
          className={cn(
            "w-[var(--radix-popover-trigger-width)] p-0",
            modal ? "z-[9999] !important" : "z-50"
          )}
          style={modal ? { zIndex: 9999 } : undefined}
          onOpenAutoFocus={(e) => {
            // Modalban ne akadályozzuk meg az autofocus-t
            if (!modal) {
              e.preventDefault()
            }
          }}
          // Modal specifikus beállítások
          sideOffset={modal ? 5 : 4}
          alignOffset={modal ? 0 : undefined}
          avoidCollisions={true}
          collisionPadding={modal ? 10 : 5}

        >
          <Command shouldFilter={false}>
            <CommandInput
              placeholder="Keresés..."
              value={searchTerm}
              onValueChange={setSearchTerm}
              autoFocus={modal}
              className={cn(
                "focus:ring-0 focus:ring-offset-0",
                modal && "pointer-events-auto"
              )}
            />
            
            {filteredOptions.length === 0 ? (
              <CommandEmpty>Nincs találat</CommandEmpty>
            ) : (
              <CommandGroup className="max-h-64 overflow-y-auto">
                {filteredOptions.map((option) => (
                  <CommandItem
                    key={option.value}
                    value={option.label}
                    onSelect={() => toggleOption(option.value)}
                    className={cn(
                      "cursor-pointer",
                      selected.includes(option.value) && "bg-gray-100 dark:bg-gray-800"
                    )}
                  >
                    <div className="mr-2 flex h-4 w-4 items-center justify-center rounded-sm border border-primary">
                      {selected.includes(option.value) && (
                        <Check className="h-3 w-3 text-primary" />
                      )}
                    </div>
                    {option.icon && (
                      <span className="mr-2 h-4 w-4 text-muted-foreground">
                        {option.icon}
                      </span>
                    )}
                    <span className="truncate">{option.label}</span>
                  </CommandItem>
                ))}
              </CommandGroup>
            )}
          </Command>
        </PopoverContent>
      </Popover>
    </div>
  )
}

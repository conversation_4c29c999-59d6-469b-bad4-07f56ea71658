<?php

namespace Tests\Feature;

use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Http;
use Tests\TestCase;

class VehicleDataControllerTest extends TestCase
{
    use RefreshDatabase;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user
        $this->user = User::factory()->create();
    }

    public function test_manufacturers_endpoint_returns_data()
    {
        // Mock the NHTSA API response
        Http::fake([
            'https://vpic.nhtsa.dot.gov/api/vehicles/getallmanufacturers?format=json' => Http::response([
                'Results' => [
                    ['Mfr_ID' => 1, 'Mfr_Name' => 'Ford'],
                    ['Mfr_ID' => 2, 'Mfr_Name' => 'Toyota'],
                ]
            ], 200)
        ]);

        $response = $this->actingAs($this->user)
            ->get('/api/vehicle-data/manufacturers');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => ['value', 'label']
                ]
            ]);
    }

    public function test_models_endpoint_requires_manufacturer_id()
    {
        $response = $this->actingAs($this->user)
            ->get('/api/vehicle-data/models');

        $response->assertStatus(422)
            ->assertJsonValidationErrors(['manufacturer_id']);
    }

    public function test_models_endpoint_returns_data_with_valid_manufacturer_id()
    {
        // Mock the NHTSA API response
        Http::fake([
            'https://vpic.nhtsa.dot.gov/api/vehicles/getmodelsformakerid/1?format=json' => Http::response([
                'Results' => [
                    ['Model_ID' => 1, 'Model_Name' => 'Focus'],
                    ['Model_ID' => 2, 'Model_Name' => 'Fiesta'],
                ]
            ], 200)
        ]);

        $response = $this->actingAs($this->user)
            ->get('/api/vehicle-data/models?manufacturer_id=1');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'success',
                'data' => [
                    '*' => ['value', 'label']
                ]
            ]);
    }

    public function test_endpoints_require_authentication()
    {
        $response = $this->get('/api/vehicle-data/manufacturers');
        $response->assertRedirect('/login');

        $response = $this->get('/api/vehicle-data/models');
        $response->assertRedirect('/login');
    }
}

import UserForm from '@/components/admin/UserForm';
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogCancel,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog';
import { Button } from '@/components/ui/button';
import { DataTable } from '@/components/ui/data-table';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, router, usePage } from '@inertiajs/react';
import { ColumnDef } from '@tanstack/react-table';
import { Download, Search } from 'lucide-react';
import { useState } from 'react';
import * as XLSX from 'xlsx';

type Role = {
    id: number;
    name: string;
};

type User = {
    id: number;
    name: string;
    email: string;
    is_active: boolean;
    roles: Role[];
    company_type: 'posta' | 'partner' | null;
    company_name: string | null;
    username: string | null;
    tax_number: string | null;
    position: string | null;
    phone: string | null;
    org_unit_id: number | null;
    garage_id: number | null;
    org_unit: { id: number; name: string } | null;
    garage: { id: number; name: string } | null;
};

type OrgUnit = { id: number; name: string };
type Garage = { id: number; name: string };
type Position = { id: string; name: string };

type PageProps = {
    users: User[];
    roles: Role[];
    orgUnits: OrgUnit[];
    garages: Garage[];
    positions: Position[];
};

const breadcrumbs: BreadcrumbItem[] = [{ title: 'Felhasználók', href: '/users' }];

export default function UserIndex() {
    const { users, roles, orgUnits, garages, positions } = usePage<PageProps>().props;

    const [open, setOpen] = useState(false);
    const [editing, setEditing] = useState<User | null>(null);
    const [globalFilter, setGlobalFilter] = useState('');
    const [deleteTarget, setDeleteTarget] = useState<User | null>(null);

    const columns: ColumnDef<User>[] = [
        { header: 'Név', accessorKey: 'name', meta: { align: 'left', sortable: true } },
        { header: 'Email', accessorKey: 'email', meta: { align: 'left', sortable: true } },
        {
            header: 'Státusz',
            accessorKey: 'is_active',
            cell: ({ row }) => (
                <span
                    className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                        row.original.is_active
                            ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                            : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                    }`}
                >
                    {row.original.is_active ? 'Aktív' : 'Inaktív'}
                </span>
            ),
            meta: { align: 'left', sortable: true },
        },
        {
            header: 'Típus',
            accessorKey: 'company_type',
            cell: ({ row }) => {
                if (!row.original.company_type) return '-';
                const isPosta = row.original.company_type === 'posta';
                return (
                    <span
                        className={`inline-flex items-center rounded-full px-2 py-1 text-xs font-medium ${
                            isPosta ? 'bg-blue-100 text-blue-800' : 'bg-purple-100 text-purple-800'
                        }`}
                    >
                        {isPosta ? 'Magyar Posta' : 'Partner'}
                    </span>
                );
            },
        },
        {
            header: 'Szerepkörök',
            accessorKey: 'roles',
            accessorFn: (row) => (row.roles?.length ? row.roles.map((r) => r.name) : ['-']),
            meta: { align: 'left', sortable: false },
        },
        {
            id: 'company_or_org_unit',
            header: 'Cég / Szervezeti egység',
            accessorFn: (row) => (row.company_type === 'posta' ? row.org_unit?.name : row.company_name),
            meta: { align: 'left', sortable: false },
        },
    ];

    const handleEdit = (user: User) => {
        setEditing(user);
        setOpen(true);
    };

    const handleAdd = () => {
        setEditing(null);
        setOpen(true);
    };

    const confirmDelete = (user: User) => {
        setDeleteTarget(user);
    };

    const performDelete = async () => {
        if (deleteTarget) {
            await router.delete(route('users.destroy', deleteTarget.id));
            setDeleteTarget(null);
        }
    };

    const handleExportExcel = (data: User[]) => {
        const exportData = data.map((user) => ({
            Név: user.name,
            Email: user.email,
            Státusz: user.is_active ? 'Aktív' : 'Inaktív',
            Típus: user.company_type === 'posta' ? 'Magyar Posta' : user.company_type === 'partner' ? 'Partner' : 'N/A',
            Szerepkörök: user.roles.map((r) => r.name).join(', '),
            'Cég / Szervezeti egység': user.company_type === 'posta' ? user.org_unit?.name : user.company_name,
        }));

        const worksheet = XLSX.utils.json_to_sheet(exportData);
        const workbook = XLSX.utils.book_new();
        XLSX.utils.book_append_sheet(workbook, worksheet, 'Felhasználók');
        XLSX.writeFile(workbook, `felhasznalok_${new Date().toISOString().slice(0, 10)}.xlsx`);
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Felhasználók" />
            <div className="flex h-full flex-1 flex-col gap-4 overflow-x-auto rounded-xl p-4">
                {' '}
                <div className="flex items-center justify-between">
                    <Button onClick={() => handleExportExcel(users)} variant="outline" size="sm" className="flex items-center gap-2">
                        <Download className="h-4 w-4" />
                        Excel export
                    </Button>
                    <div className="flex items-center gap-3">
                        <div className="relative">
                            <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 transform text-gray-400" />
                            <Input
                                type="text"
                                placeholder="Keresés..."
                                value={globalFilter}
                                onChange={(e) => setGlobalFilter(e.target.value)}
                                className="w-64 border-gray-200 pl-10 shadow-sm focus:border-blue-500 focus:ring-blue-500/20"
                            />
                        </div>
                        <Dialog open={open} onOpenChange={setOpen}>
                            <DialogTrigger asChild>
                                <Button onClick={handleAdd} className="bg-primary text-white shadow-sm hover:cursor-pointer hover:bg-green-600">
                                    + Új felhasználó
                                </Button>
                            </DialogTrigger>
                            <DialogContent
                                className="flex max-h-[90vh] max-w-2xl flex-col p-0"
                                onInteractOutside={(e) => {
                                    // Megakadályozzuk, hogy a dialog bezáródjon háttérkattintásra
                                    e.preventDefault();
                                }}
                            >
                                <DialogHeader className="p-6 pb-0">
                                    <DialogTitle>{editing ? 'Felhasználó szerkesztése' : 'Új felhasználó'}</DialogTitle>
                                    <DialogDescription>Töltsd ki az alábbi űrlapot a felhasználó adatainak mentéséhez.</DialogDescription>
                                </DialogHeader>

                                <div className="flex-1 overflow-y-auto px-6">
                                    <UserForm
                                        user={editing}
                                        roles={roles}
                                        orgUnits={orgUnits}
                                        garages={garages}
                                        positions={positions}
                                        onClose={() => setOpen(false)}
                                    />
                                </div>
                            </DialogContent>
                        </Dialog>
                    </div>
                </div>
                <DataTable
                    columns={columns}
                    data={users}
                    onEdit={handleEdit}
                    onDelete={confirmDelete}
                    globalFilter={globalFilter}
                    exportFileName="felhasznalok"
                    pageSize={10}
                    showExportButton={false}
                />
            </div>

            <AlertDialog open={!!deleteTarget} onOpenChange={(open) => !open && setDeleteTarget(null)}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Biztosan inaktiválni szeretnéd ezt a felhasználót?</AlertDialogTitle>
                        <AlertDialogDescription>
                            A(z) <strong>{deleteTarget?.name}</strong> nevű felhasználó inaktívvá válik és nem tud majd bejelentkezni. Később újra
                            aktiválhatod a felhasználót szerkesztéssel.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Mégsem</AlertDialogCancel>
                        <AlertDialogAction onClick={performDelete} className="bg-orange-600 text-white hover:bg-orange-700">
                            Inaktiválás
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>
        </AppLayout>
    );
}

import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { MultiSelect } from '@/components/ui/multi-select';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { SingleSelect } from '@/components/ui/single-select';
import { useForm } from '@inertiajs/react';
import { Briefcase, Building2, Car, Handshake, Hash, Lock, Mail, MailCheck, Phone, ShieldCheck, UserCheck, User as UserIcon } from 'lucide-react';
import { useEffect, useState } from 'react';
import { route } from 'ziggy-js';

type Role = {
    id: number;
    name: string;
};

type OrgUnit = { id: number; name: string };
type Garage = { id: number; name: string };
type Position = { id: string; name: string };

type Contact = {
    id: number;
    name: string;
    email: string;
    username: string;
    org_unit_id?: number;
    garage_id?: number;
    org_unit?: { id: number; name: string };
    garage?: { id: number; name: string };
};

type Props = {
    user: {
        id?: number;
        name: string;
        email: string;
        username?: string | null;
        is_active?: boolean;
        roles: Role[];
        company_type: 'posta' | 'partner' | null;
        company_name?: string | null;
        tax_number?: string | null;
        position?: string | null;
        phone?: string | null;
        org_unit_id?: number | null;
        garage_id?: number | null;
    } | null;
    roles: Role[];
    orgUnits: OrgUnit[];
    garages: Garage[];
    positions: Position[];
    onClose: () => void;
};

export default function UserForm({ user, roles, orgUnits, garages, positions, onClose }: Props) {
    const { data, setData, post, put, processing, errors, reset } = useForm({
        name: user?.name || '',
        email: user?.email || '',
        password: '',
        password_confirmation: '',
        is_active: user?.is_active ?? true,
        roles: user?.roles?.map((r) => r.id) || [],
        username: user?.username || '',
        company_type: user?.company_type || 'partner',
        company_name: user?.company_name || '',
        tax_number: user?.tax_number || '',
        position: user?.position || '',
        phone: user?.phone || '',
        org_unit_id: user?.org_unit_id?.toString() || '',
        garage_id: user?.garage_id?.toString() || '',
    });

    const [positionList, setPositionList] = useState<{ value: string; label: string }[]>(
        positions.map((pos) => ({ value: pos.id, label: pos.name })),
    );

    const handlePositionChange = (value: string | number | null) => {
        const val = value ? String(value) : '';
        setData('position', val);
        if (val && !positionList.find((opt) => opt.value === val)) {
            setPositionList((prev) => [...prev, { value: val, label: val }]);
        }
    };

    const [contactData, setContactData] = useState<Contact | null>(null);
    const [isPostaDomain, setIsPostaDomain] = useState(false);
    const [isContactFound, setIsContactFound] = useState(false);

    useEffect(() => () => reset(), []);

    // Email validáció és contact lookup
    useEffect(() => {
        const email = data.email.toLowerCase();
        const isPostaEmail = email.endsWith('@posta.hu');
        setIsPostaDomain(isPostaEmail);

        if (data.company_type === 'partner' && isPostaEmail) {
            setData('company_type', 'posta');
            return;
        }

        if (data.company_type === 'posta' && email.length > 3) {
            fetch(route('api.contacts.lookup', { email: email }))
                .then((response) => response.json())
                .then((result) => {
                    if (result.success && result.contact) {
                        setContactData(result.contact);
                        setIsContactFound(true);
                        setData((prev) => ({
                            ...prev,
                            name: result.contact.name,
                            username: result.contact.username,
                            org_unit_id: result.contact.org_unit_id?.toString() || '',
                            garage_id: result.contact.garage_id?.toString() || '',
                        }));
                    } else {
                        setContactData(null);
                        setIsContactFound(false);
                    }
                })
                .catch(() => {
                    setContactData(null);
                    setIsContactFound(false);
                });
        } else {
            setContactData(null);
            setIsContactFound(false);
            if (data.company_type === 'posta' && !user) {
                setData((prev) => ({ ...prev, name: '', username: '', org_unit_id: '', garage_id: '' }));
            }
        }
    }, [data.email, data.company_type, user]);

    // Company type változás kezelése
    useEffect(() => {
        if (data.company_type === 'posta') {
            setData((prev) => ({ ...prev, company_name: '', tax_number: '', position: '', phone: '' }));
        } else if (data.company_type === 'partner') {
            setData((prev) => ({
                ...prev,
                username: '',
                org_unit_id: '',
                garage_id: '',
            }));
        }
    }, [data.company_type]);

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        const url = user ? `/admin/users/${user.id}` : '/admin/users';
        const method = user ? put : post;
        method(url, { onSuccess: () => onClose() });
    };

    return (
        <form onSubmit={handleSubmit} className="flex h-full flex-col">
            <div className="grid grid-cols-1 gap-x-6 gap-y-6 py-4 md:grid-cols-2">
                <div className="grid gap-2 md:col-span-2">
                    <RadioGroup
                        value={data.company_type}
                        onValueChange={(value) => setData('company_type', value as 'posta' | 'partner')}
                        className="flex items-center gap-6"
                    >
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="posta" id="posta" />
                            <label htmlFor="posta" className="flex cursor-pointer items-center gap-2">
                                <MailCheck className="h-4 w-4 text-muted-foreground" />
                                <span>Magyar Posta Zrt.</span>
                            </label>
                        </div>
                        <div className="flex items-center space-x-2">
                            <RadioGroupItem value="partner" id="partner" />
                            <label htmlFor="partner" className="flex cursor-pointer items-center gap-2">
                                <Handshake className="h-4 w-4 text-muted-foreground" />
                                <span>Más partner</span>
                            </label>
                        </div>
                    </RadioGroup>
                    {errors.company_type && <p className="text-sm text-red-500">{errors.company_type}</p>}
                </div>

                <div className="space-y-2">
                    <Label htmlFor="email">Email</Label>
                    <div className="relative">
                        <Mail className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                        <Input id="email" type="email" className="pl-10" value={data.email} onChange={(e) => setData('email', e.target.value)} />
                    </div>
                    {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
                </div>

                <div className="space-y-2">
                    <Label htmlFor="name">Név</Label>
                    <div className="relative">
                        <UserIcon className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                        <Input
                            id="name"
                            className="pl-10"
                            value={data.name}
                            onChange={(e) => setData('name', e.target.value)}
                            disabled={processing || (isPostaDomain && isContactFound)}
                        />
                    </div>
                    {errors.name && <p className="text-sm text-red-500">{errors.name}</p>}
                </div>

                {data.company_type === 'posta' && (
                    <div className="space-y-2">
                        <Label htmlFor="username">Felhasználónév</Label>
                        <div className="relative">
                            <UserIcon className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                            <Input
                                id="username"
                                className="pl-10"
                                value={data.username}
                                onChange={(e) => setData('username', e.target.value)}
                                disabled={processing || (isPostaDomain && isContactFound)}
                            />
                        </div>
                        {errors.username && <p className="text-sm text-red-500">{errors.username}</p>}
                    </div>
                )}

                <div className="space-y-2">
                    <Label htmlFor="password">Jelszó {user && <span className="text-sm text-muted-foreground">(opcionális)</span>}</Label>
                    <div className="relative">
                        <Lock className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                        <Input
                            id="password"
                            type="password"
                            className="pl-10"
                            value={data.password}
                            onChange={(e) => setData('password', e.target.value)}
                            placeholder={user ? 'Hagyd üresen, ha nem változtatod' : 'Jelszó'}
                        />
                    </div>
                    {errors.password && <p className="text-sm text-red-500">{errors.password}</p>}
                </div>

                <div className="space-y-2">
                    <Label htmlFor="password_confirmation">
                        Jelszó megerősítése {user && <span className="text-sm text-muted-foreground">(opcionális)</span>}
                    </Label>
                    <div className="relative">
                        <Lock className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                        <Input
                            id="password_confirmation"
                            type="password"
                            className="pl-10"
                            value={data.password_confirmation}
                            onChange={(e) => setData('password_confirmation', e.target.value)}
                            placeholder={user ? 'Hagyd üresen, ha nem változtatod' : 'Jelszó megerősítése'}
                        />
                    </div>
                    {errors.password_confirmation && <p className="text-sm text-red-500">{errors.password_confirmation}</p>}
                </div>

                {data.company_type === 'posta' && (
                    <>
                        <div className="space-y-2">
                            <Label htmlFor="org_unit_id">Szervezeti egység</Label>
                            <SingleSelect
                                value={data.org_unit_id}
                                onChange={(value) => setData('org_unit_id', value ? String(value) : '')}
                                options={[
                                    { value: '', label: 'Válasszon szervezeti egységet...' },
                                    ...orgUnits.map((unit) => ({
                                        value: unit.id.toString(),
                                        label: unit.name,
                                    })),
                                ]}
                                placeholder="Szervezeti egység"
                                icon={<Building2 className="h-4 w-4 text-muted-foreground" />}
                                disabled={processing}
                            />
                            {errors.org_unit_id && <p className="text-sm text-red-500">{errors.org_unit_id}</p>}
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="garage_id">Posta Garázs</Label>
                            <SingleSelect
                                value={data.garage_id}
                                onChange={(value) => setData('garage_id', value ? String(value) : '')}
                                options={[
                                    { value: '', label: 'Válasszon garázst...' },
                                    ...garages.map((garage) => ({
                                        value: garage.id.toString(),
                                        label: garage.name,
                                    })),
                                ]}
                                placeholder="Garázs"
                                icon={<Car className="h-4 w-4 text-muted-foreground" />}
                                disabled={processing}
                            />
                            {errors.garage_id && <p className="text-sm text-red-500">{errors.garage_id}</p>}
                        </div>
                    </>
                )}

                {data.company_type === 'partner' && (
                    <>
                        <div className="space-y-2">
                            <Label htmlFor="company_name">Cégnév</Label>
                            <div className="relative">
                                <Building2 className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="company_name"
                                    className="pl-10"
                                    value={data.company_name}
                                    onChange={(e) => setData('company_name', e.target.value)}
                                />
                            </div>
                            {errors.company_name && <p className="text-sm text-red-500">{errors.company_name}</p>}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="tax_number">Adószám</Label>
                            <div className="relative">
                                <Hash className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="tax_number"
                                    className="pl-10"
                                    value={data.tax_number}
                                    onChange={(e) => setData('tax_number', e.target.value)}
                                />
                            </div>
                            {errors.tax_number && <p className="text-sm text-red-500">{errors.tax_number}</p>}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="position">Munkakör</Label>
                            <SingleSelect
                                value={data.position}
                                onChange={handlePositionChange}
                                options={positionList}
                                placeholder="Munkakör"
                                icon={<Briefcase className="h-4 w-4 text-muted-foreground" />}
                                allowCustomValue={true}
                                createPrefix="Új munkakör: "
                                disabled={processing}
                            />
                            {errors.position && <p className="text-sm text-red-500">{errors.position}</p>}
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="phone">Telefonszám</Label>
                            <div className="relative">
                                <Phone className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="phone"
                                    type="tel"
                                    className="pl-10"
                                    value={data.phone}
                                    onChange={(e) => setData('phone', e.target.value)}
                                />
                            </div>
                            {errors.phone && <p className="text-sm text-red-500">{errors.phone}</p>}
                        </div>
                    </>
                )}

                <div className="space-y-2 md:col-span-2">
                    <Label>Szerepkörök</Label>
                    <MultiSelect
                        options={roles.map((r) => ({ label: r.name, value: r.id }))}
                        selected={data.roles}
                        onChange={(values) => setData('roles', values.map(Number))}
                        placeholder="Szerepkörök..."
                        icon={<ShieldCheck className="h-4 w-4" />}
                    />
                    {errors.roles && <p className="text-sm text-red-500">{errors.roles}</p>}
                </div>

                <div className="space-y-2 md:col-span-2">
                    <div className="flex items-center space-x-2">
                        <Checkbox id="is_active" checked={data.is_active} onCheckedChange={(checked) => setData('is_active', checked as boolean)} />
                        <Label htmlFor="is_active" className="flex items-center gap-2">
                            <UserCheck className="h-4 w-4" />
                            Aktív felhasználó
                        </Label>
                    </div>
                    <p className="text-sm text-muted-foreground">Inaktív felhasználók nem tudnak bejelentkezni a rendszerbe.</p>
                    {errors.is_active && <p className="text-sm text-red-500">{errors.is_active}</p>}
                </div>
            </div>

            <DialogFooter className="mt-auto bg-slate-50 px-6 py-3">
                <Button type="button" variant="outline" onClick={onClose}>
                    Mégse
                </Button>
                <Button type="submit" disabled={processing}>
                    {user ? 'Mentés' : 'Létrehozás'}
                </Button>
            </DialogFooter>
        </form>
    );
}

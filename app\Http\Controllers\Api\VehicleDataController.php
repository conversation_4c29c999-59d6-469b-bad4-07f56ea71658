<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Cache;
use Illuminate\Http\JsonResponse;

class VehicleDataController extends Controller
{
    private const NHTSA_BASE_URL = 'https://vpic.nhtsa.dot.gov/api';
    private const CACHE_TTL = 3600; // 1 hour cache

    /**
     * Get all vehicle manufacturers from NHTSA API
     */
    public function getManufacturers(): JsonResponse
    {
        try {
            $manufacturers = Cache::remember('vehicle_manufacturers', self::CACHE_TTL, function () {
                $response = Http::timeout(30)->get(self::NHTSA_BASE_URL . '/vehicles/GetMakesForVehicleType/car', [
                    'format' => 'json'
                ]);

                if (!$response->successful()) {
                    throw new \Exception('Failed to fetch manufacturers from NHTSA API');
                }

                $data = $response->json();
                dd($data);
                
                if (!isset($data['Results']) || !is_array($data['Results'])) {
                    throw new \Exception('Invalid response format from NHTSA API');
                }

                // Transform the data to match our frontend expectations
                return collect($data['Results'])
                    ->map(function ($make) {
                        return [
                            'id' => $make['MakeID'],
                            'name' => $make['MakeName'],
                            'value' => $make['MakeName'], // For SingleSelect component
                            'label' => $make['MakeName']  // For SingleSelect component
                        ];
                    })
                    ->sortBy('name')
                    ->values()
                    ->toArray();
            });

            return response()->json([
                'success' => true,
                'data' => $manufacturers
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch manufacturers: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get vehicle models for a specific manufacturer from NHTSA API
     */
    public function getModels(Request $request): JsonResponse
    {
        $request->validate([
            'manufacturer' => 'required|string|max:255'
        ]);

        $manufacturer = $request->input('manufacturer');

        try {
            $cacheKey = 'vehicle_models_' . md5(strtolower($manufacturer));
            
            $models = Cache::remember($cacheKey, self::CACHE_TTL, function () use ($manufacturer) {
                $response = Http::timeout(30)->get(self::NHTSA_BASE_URL . '/vehicles/getmodelsformake/' . urlencode($manufacturer), [
                    'format' => 'json'
                ]);

                if (!$response->successful()) {
                    throw new \Exception('Failed to fetch models from NHTSA API');
                }

                $data = $response->json();
                
                if (!isset($data['Results']) || !is_array($data['Results'])) {
                    throw new \Exception('Invalid response format from NHTSA API');
                }

                // Transform the data to match our frontend expectations
                return collect($data['Results'])
                    ->map(function ($model) {
                        return [
                            'id' => $model['Model_ID'],
                            'name' => $model['Model_Name'],
                            'value' => $model['Model_Name'], // For SingleSelect component
                            'label' => $model['Model_Name']  // For SingleSelect component
                        ];
                    })
                    ->unique('name') // Remove duplicates
                    ->sortBy('name')
                    ->values()
                    ->toArray();
            });

            return response()->json([
                'success' => true,
                'data' => $models
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to fetch models: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Clear vehicle data cache (for admin use)
     */
    public function clearCache(): JsonResponse
    {
        try {
            Cache::forget('vehicle_manufacturers');
            
            // Clear all model caches (this is a bit brute force, but effective)
            $cacheKeys = Cache::getRedis()->keys('*vehicle_models_*');
            foreach ($cacheKeys as $key) {
                Cache::forget(str_replace(config('cache.prefix') . ':', '', $key));
            }

            return response()->json([
                'success' => true,
                'message' => 'Vehicle data cache cleared successfully'
            ]);

        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Failed to clear cache: ' . $e->getMessage()
            ], 500);
        }
    }
}

import { Button } from '@/components/ui/button';
import { DialogFooter } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { SingleSelect } from '@/components/ui/single-select';
import { useForm } from '@inertiajs/react';
import axios from 'axios';
import { Calendar, CalendarCheck, Car, FileText, Hash, Mail, ScanLine, User } from 'lucide-react';
import { FormEventHandler, useEffect, useState } from 'react';

interface PostalVehicle {
    id?: number;
    license_plate: string;
    type?: string; // Keep for backward compatibility
    manufacturer: string;
    model: string;
    chassis_number: string;
    category: 'N1' | 'N2' | 'N3';
    contract_classification: string;
    manufacturing_year: number;
    fuel_type: string;
    authority_inspection_valid_until: string;
    garage_id: number;
    garage_central_email: string;
    service_provider_id: number;
    technical_staff_name: string;
    technical_staff_email: string;
    technical_manager_name: string;
    technical_manager_email: string;
}

interface Garage {
    id: number;
    name: string;
    postal_code?: string;
    city?: string;
    address?: string;
}

interface ServiceProvider {
    id: number;
    name: string;
    city: string;
}

interface VehicleManufacturer {
    id: number;
    name: string;
    value: string;
    label: string;
}

interface VehicleModel {
    id: number;
    name: string;
    value: string;
    label: string;
}

interface PostalVehicleFormProps {
    vehicle?: PostalVehicle | null;
    garages: Garage[];
    serviceProviders: ServiceProvider[];
    onCancel: () => void;
}

export default function PostalVehicleForm({ vehicle, garages, serviceProviders, onCancel }: PostalVehicleFormProps) {
    const { data, setData, post, put, processing, errors, wasSuccessful } = useForm({
        license_plate: vehicle?.license_plate || '',
        manufacturer: vehicle?.manufacturer || '',
        model: vehicle?.model || '',
        chassis_number: vehicle?.chassis_number || '',
        category: vehicle?.category || 'N1',
        contract_classification: vehicle?.contract_classification || '',
        manufacturing_year: vehicle?.manufacturing_year || new Date().getFullYear(),
        fuel_type: vehicle?.fuel_type || '',
        authority_inspection_valid_until: vehicle?.authority_inspection_valid_until || '',
        garage_id: vehicle?.garage_id || '',
        garage_central_email: vehicle?.garage_central_email || '',
        service_provider_id: vehicle?.service_provider_id || '',
        technical_staff_name: vehicle?.technical_staff_name || '',
        technical_staff_email: vehicle?.technical_staff_email || '',
        technical_manager_name: vehicle?.technical_manager_name || '',
        technical_manager_email: vehicle?.technical_manager_email || '',
    });

    // State for vehicle data
    const [manufacturers, setManufacturers] = useState<VehicleManufacturer[]>([]);
    const [models, setModels] = useState<VehicleModel[]>([]);
    const [loadingManufacturers, setLoadingManufacturers] = useState(false);
    const [loadingModels, setLoadingModels] = useState(false);

    // Load manufacturers on component mount
    useEffect(() => {
        loadManufacturers();
    }, []);

    // Load models when manufacturer changes
    useEffect(() => {
        if (data.manufacturer) {
            loadModels(data.manufacturer);
        } else {
            setModels([]);
        }
    }, [data.manufacturer]);

    useEffect(() => {
        if (wasSuccessful) {
            onCancel();
        }
    }, [wasSuccessful, onCancel]);

    const loadManufacturers = async () => {
        setLoadingManufacturers(true);
        try {
            const response = await axios.get('/api/vehicle-data/manufacturers');
            if (response.data.success) {
                setManufacturers(response.data.data);
            }
        } catch (error) {
            console.error('Failed to load manufacturers:', error);
        } finally {
            setLoadingManufacturers(false);
        }
    };

    const loadModels = async (manufacturer: string) => {
        setLoadingModels(true);
        try {
            const response = await axios.get('/api/vehicle-data/models', {
                params: { manufacturer },
            });
            if (response.data.success) {
                setModels(response.data.data);
            }
        } catch (error) {
            console.error('Failed to load models:', error);
        } finally {
            setLoadingModels(false);
        }
    };

    const handleManufacturerChange = (value: string | number | null) => {
        setData('manufacturer', value ? String(value) : '');
        setData('model', ''); // Reset model when manufacturer changes
    };

    const handleSubmit: FormEventHandler = (e) => {
        e.preventDefault();

        if (vehicle?.id) {
            put(route('admin.postal-vehicles.update', vehicle.id));
        } else {
            post(route('admin.postal-vehicles.store'));
        }
    };

    const categoryOptions = [
        { value: 'N1', label: 'N1 - Könnyű tehergépjármű' },
        { value: 'N2', label: 'N2 - Közepes tehergépjármű' },
        { value: 'N3', label: 'N3 - Nehéz tehergépjármű' },
    ];

    const garageOptions = garages.map((garage) => ({
        value: garage.id.toString(),
        label: garage.name,
    }));

    const serviceProviderOptions = serviceProviders.map((provider) => ({
        value: provider.id.toString(),
        label: `${provider.name} (${provider.city})`,
    }));

    const fuelTypeOptions = [
        { value: 'Benzin', label: 'Benzin' },
        { value: 'Dízel', label: 'Dízel' },
        { value: 'Elektromos', label: 'Elektromos' },
        { value: 'Hibrid', label: 'Hibrid' },
        { value: 'CNG', label: 'CNG (Földgáz)' },
        { value: 'LPG', label: 'LPG (Autógáz)' },
    ];

    return (
        <form onSubmit={handleSubmit} className="flex flex-1 flex-col overflow-hidden">
            <div className="flex-1 space-y-6 overflow-y-auto p-6">
                {/* Alapadatok */}
                <div className="space-y-4">
                    <h4 className="text-sm font-medium text-gray-900">Alapadatok</h4>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <Label htmlFor="license_plate">Rendszám *</Label>
                            <div className="relative">
                                <Hash className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="license_plate"
                                    tabIndex={1}
                                    value={data.license_plate}
                                    onChange={(e) => setData('license_plate', e.target.value.toUpperCase())}
                                    placeholder="ABC-123"
                                    className="pl-10 font-mono"
                                    required
                                />
                            </div>
                            {errors.license_plate && <p className="text-sm text-red-600">{errors.license_plate}</p>}
                        </div>

                        <div>
                            <Label htmlFor="manufacturer">Gyártmány *</Label>
                            <SingleSelect
                                value={data.manufacturer}
                                onChange={handleManufacturerChange}
                                options={manufacturers}
                                placeholder="Válassz gyártmányt..."
                                disabled={loadingManufacturers}
                            />
                            {errors.manufacturer && <p className="text-sm text-red-600">{errors.manufacturer}</p>}
                        </div>

                        <div>
                            <Label htmlFor="model">Modell *</Label>
                            <SingleSelect
                                value={data.model}
                                onChange={(value: string | number | null) => setData('model', value ? String(value) : '')}
                                options={models}
                                placeholder={data.manufacturer ? 'Válassz modellt...' : 'Először válassz gyártmányt'}
                                disabled={!data.manufacturer || loadingModels}
                            />
                            {errors.model && <p className="text-sm text-red-600">{errors.model}</p>}
                        </div>

                        <div>
                            <Label htmlFor="chassis_number">Alvázszám *</Label>
                            <div className="relative">
                                <ScanLine className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="chassis_number"
                                    tabIndex={4}
                                    value={data.chassis_number}
                                    onChange={(e) => setData('chassis_number', e.target.value)}
                                    placeholder="VIN szám"
                                    className="pl-10 font-mono"
                                    required
                                />
                            </div>
                            {errors.chassis_number && <p className="text-sm text-red-600">{errors.chassis_number}</p>}
                        </div>

                        <div>
                            <Label htmlFor="category">Kategória *</Label>
                            <SingleSelect
                                icon={<Car className="h-4 w-4 text-muted-foreground" />}
                                options={categoryOptions}
                                value={data.category}
                                onChange={(value) => setData('category', value as 'N1' | 'N2' | 'N3')}
                                placeholder="Válasszon kategóriát"
                                tabIndex={5}
                            />
                            {errors.category && <p className="text-sm text-red-600">{errors.category}</p>}
                        </div>

                        <div>
                            <Label htmlFor="contract_classification">Szerződés besorolás *</Label>
                            <div className="relative">
                                <FileText className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="contract_classification"
                                    value={data.contract_classification}
                                    onChange={(e) => setData('contract_classification', e.target.value)}
                                    placeholder="Szerződés típusa"
                                    className="pl-10"
                                    required
                                />
                            </div>
                            {errors.contract_classification && <p className="text-sm text-red-600">{errors.contract_classification}</p>}
                        </div>

                        <div>
                            <Label htmlFor="manufacturing_year">Gyártási év *</Label>
                            <div className="relative">
                                <Calendar className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="manufacturing_year"
                                    type="number"
                                    min="1900"
                                    max={new Date().getFullYear() + 1}
                                    value={data.manufacturing_year}
                                    onChange={(e) => setData('manufacturing_year', parseInt(e.target.value))}
                                    className="pl-10"
                                    required
                                />
                            </div>
                            {errors.manufacturing_year && <p className="text-sm text-red-600">{errors.manufacturing_year}</p>}
                        </div>

                        <div>
                            <Label htmlFor="fuel_type">Üzemanyag *</Label>
                            <SingleSelect
                                icon={<Car className="h-4 w-4 text-muted-foreground" />}
                                options={fuelTypeOptions}
                                value={data.fuel_type}
                                onChange={(value) => setData('fuel_type', String(value))}
                                placeholder="Válasszon üzemanyag típust"
                                allowCustomValue
                            />
                            {errors.fuel_type && <p className="text-sm text-red-600">{errors.fuel_type}</p>}
                        </div>

                        <div>
                            <Label htmlFor="authority_inspection_valid_until">Hatósági vizsga érvényessége *</Label>
                            <div className="relative">
                                <CalendarCheck className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="authority_inspection_valid_until"
                                    type="date"
                                    value={data.authority_inspection_valid_until}
                                    onChange={(e) => setData('authority_inspection_valid_until', e.target.value)}
                                    className="pl-10"
                                    required
                                />
                            </div>
                            {errors.authority_inspection_valid_until && (
                                <p className="text-sm text-red-600">{errors.authority_inspection_valid_until}</p>
                            )}
                        </div>
                    </div>
                </div>

                {/* Garázs és szerviz adatok */}
                <div className="space-y-4">
                    <h4 className="text-sm font-medium text-gray-900">Garázs és szerviz</h4>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <Label htmlFor="garage_id">Területi illetékes garázs *</Label>
                            <SingleSelect
                                icon={<Car className="h-4 w-4 text-muted-foreground" />}
                                options={garageOptions}
                                value={data.garage_id.toString()}
                                onChange={(value) => setData('garage_id', parseInt(String(value)))}
                                placeholder="Válasszon garázsot"
                            />
                            {errors.garage_id && <p className="text-sm text-red-600">{errors.garage_id}</p>}
                        </div>

                        <div>
                            <Label htmlFor="garage_central_email">Garázs központi email *</Label>
                            <div className="relative">
                                <Mail className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="garage_central_email"
                                    type="email"
                                    value={data.garage_central_email}
                                    onChange={(e) => setData('garage_central_email', e.target.value)}
                                    placeholder="<EMAIL>"
                                    className="pl-10"
                                    required
                                />
                            </div>
                            {errors.garage_central_email && <p className="text-sm text-red-600">{errors.garage_central_email}</p>}
                        </div>

                        <div className="md:col-span-2">
                            <Label htmlFor="service_provider_id">Területileg javításra kijelölt szerviz *</Label>
                            <SingleSelect
                                icon={<User className="h-4 w-4 text-muted-foreground" />}
                                options={serviceProviderOptions}
                                value={data.service_provider_id.toString()}
                                onChange={(value) => setData('service_provider_id', parseInt(String(value)))}
                                placeholder="Válasszon szervizt"
                            />
                            {errors.service_provider_id && <p className="text-sm text-red-600">{errors.service_provider_id}</p>}
                        </div>
                    </div>
                </div>

                {/* Járműmenedzsment adatok */}
                <div className="space-y-4">
                    <h4 className="text-sm font-medium text-gray-900">Járműmenedzsment munkatársak</h4>

                    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <Label htmlFor="technical_staff_name">Műszaki munkatárs neve *</Label>
                            <div className="relative">
                                <User className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="technical_staff_name"
                                    value={data.technical_staff_name}
                                    onChange={(e) => setData('technical_staff_name', e.target.value)}
                                    placeholder="Kovács János"
                                    className="pl-10"
                                    required
                                />
                            </div>
                            {errors.technical_staff_name && <p className="text-sm text-red-600">{errors.technical_staff_name}</p>}
                        </div>

                        <div>
                            <Label htmlFor="technical_staff_email">Műszaki munkatárs email *</Label>
                            <div className="relative">
                                <Mail className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="technical_staff_email"
                                    type="email"
                                    value={data.technical_staff_email}
                                    onChange={(e) => setData('technical_staff_email', e.target.value)}
                                    placeholder="<EMAIL>"
                                    className="pl-10"
                                    required
                                />
                            </div>
                            {errors.technical_staff_email && <p className="text-sm text-red-600">{errors.technical_staff_email}</p>}
                        </div>

                        <div>
                            <Label htmlFor="technical_manager_name">Műszaki menedzser neve *</Label>
                            <div className="relative">
                                <User className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="technical_manager_name"
                                    value={data.technical_manager_name}
                                    onChange={(e) => setData('technical_manager_name', e.target.value)}
                                    placeholder="Nagy Péter"
                                    className="pl-10"
                                    required
                                />
                            </div>
                            {errors.technical_manager_name && <p className="text-sm text-red-600">{errors.technical_manager_name}</p>}
                        </div>

                        <div>
                            <Label htmlFor="technical_manager_email">Műszaki menedzser email *</Label>
                            <div className="relative">
                                <Mail className="absolute top-3 left-3 h-4 w-4 text-muted-foreground" />
                                <Input
                                    id="technical_manager_email"
                                    type="email"
                                    value={data.technical_manager_email}
                                    onChange={(e) => setData('technical_manager_email', e.target.value)}
                                    placeholder="<EMAIL>"
                                    className="pl-10"
                                    required
                                />
                            </div>
                            {errors.technical_manager_email && <p className="text-sm text-red-600">{errors.technical_manager_email}</p>}
                        </div>
                    </div>
                </div>
            </div>

            <DialogFooter className="flex-shrink-0 p-6">
                <Button type="button" variant="outline" onClick={onCancel}>
                    Mégse
                </Button>
                <Button type="submit" disabled={processing} className="bg-primary text-white hover:bg-green-600">
                    {processing ? 'Mentés...' : vehicle ? 'Frissítés' : 'Létrehozás'}
                </Button>
            </DialogFooter>
        </form>
    );
}

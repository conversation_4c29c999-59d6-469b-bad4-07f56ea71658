<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\PostalVehicle;
use App\Models\Garage;
use App\Models\ServiceProvider;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Maatwebsite\Excel\Validators\ValidationException;
use Illuminate\Http\RedirectResponse;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\PostalVehicleImport;
use App\Exports\PostalVehicleExport;

class PostalVehicleController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $vehicles = PostalVehicle::with(['garage', 'serviceProvider'])
            ->orderBy('license_plate')
            ->get();

        $garages = Garage::orderBy('name')->get();
        $serviceProviders = ServiceProvider::active()->orderBy('name')->get();

        return Inertia::render('admin/postal-vehicles/index', [
            'vehicles' => $vehicles,
            'garages' => $garages,
            'serviceProviders' => $serviceProviders,
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request): RedirectResponse
    {
        $validated = $request->validate([
            'license_plate' => 'required|string|max:20|unique:postal_vehicles,license_plate',
            'type' => 'required|string|max:255',
            'chassis_number' => 'required|string|max:255|unique:postal_vehicles,chassis_number',
            'category' => 'required|in:N1,N2,N3',
            'contract_classification' => 'required|string|max:255',
            'manufacturing_year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'fuel_type' => 'required|string|max:100',
            'authority_inspection_valid_until' => 'required|date|after:today',
            'garage_id' => 'required|exists:garages,id',
            'garage_central_email' => 'required|email|max:255',
            'service_provider_id' => 'required|exists:service_providers,id',
            'technical_staff_name' => 'required|string|max:255',
            'technical_staff_email' => 'required|email|max:255',
            'technical_manager_name' => 'required|string|max:255',
            'technical_manager_email' => 'required|email|max:255',
        ]);

        PostalVehicle::create($validated);

        return redirect()
            ->route('admin.postal-vehicles.index')
            ->with('success', 'Postai jármű sikeresen létrehozva!');
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PostalVehicle $postalVehicle): RedirectResponse
    {
        $validated = $request->validate([
            'license_plate' => 'required|string|max:20|unique:postal_vehicles,license_plate,' . $postalVehicle->id,
            'type' => 'required|string|max:255',
            'chassis_number' => 'required|string|max:255|unique:postal_vehicles,chassis_number,' . $postalVehicle->id,
            'category' => 'required|in:N1,N2,N3',
            'contract_classification' => 'required|string|max:255',
            'manufacturing_year' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'fuel_type' => 'required|string|max:100',
            'authority_inspection_valid_until' => 'required|date|after:today',
            'garage_id' => 'required|exists:garages,id',
            'garage_central_email' => 'required|email|max:255',
            'service_provider_id' => 'required|exists:service_providers,id',
            'technical_staff_name' => 'required|string|max:255',
            'technical_staff_email' => 'required|email|max:255',
            'technical_manager_name' => 'required|string|max:255',
            'technical_manager_email' => 'required|email|max:255',
        ]);

        $postalVehicle->update($validated);

        return redirect()
            ->route('admin.postal-vehicles.index')
            ->with('success', 'Postai jármű sikeresen frissítve!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PostalVehicle $postalVehicle): RedirectResponse
    {
        $postalVehicle->delete();

        return redirect()
            ->route('admin.postal-vehicles.index')
            ->with('success', 'Postai jármű sikeresen törölve!');
    }

    /**
     * Export vehicles to Excel
     */
    public function export()
    {
        return Excel::download(new PostalVehicleExport, 'postai_jarmuvek.xlsx');
    }

    /**
     * Import vehicles from Excel
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv|max:10240', // 10MB max
        ]);

        try {
            Excel::import(new PostalVehicleImport(), $request->file('file'));

            return redirect()
                ->route('admin.postal-vehicles.index')
                ->with('success', 'Postai járművek sikeresen importálva!');
        } catch (ValidationException $e) {
            $failures = $e->failures();
            $errors = [];
            foreach ($failures as $failure) {
                $errors[] = 'Sor: ' . $failure->row() . ' - ' . implode(', ', $failure->errors());
            }
            return redirect()
                ->route('admin.postal-vehicles.index')
                ->with('import_errors', $errors);
        } catch (\Exception $e) {
            return redirect()
                ->route('admin.postal-vehicles.index')
                ->with('error', 'Hiba történt az importálás során: ' . $e->getMessage());
        }
    }

    /**
     * Download the sample import file.
     */
    public function downloadSample()
    {
        $path = storage_path('app/samples/postai_jarmuvek_minta.csv');

        if (!file_exists($path)) {
            abort(404, 'A minta fájl nem található.');
        }

        return response()->download($path, 'postai_jarmuvek_minta.csv');
    }
}
